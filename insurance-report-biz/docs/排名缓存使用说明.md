# 排名缓存使用说明

## 概述

为了提高排名查询的性能，我们为`pageAssessConvertRank`（标准保费排名）和`pagePromotionRank`（推广费排名）方法添加了Redis缓存功能。

## 缓存策略

### 缓存时间
- **过期时间**: 缓存数据会在当天24点自动过期清除
- **计算方式**: 动态计算到当天24点的剩余秒数作为TTL
- **最小保留**: 至少保留60秒，避免立即过期

### 缓存Key规则

#### 标准保费排名缓存Key
```
assistant:rank:assessConvert:{pt}:{timeDim}:{page}:{size}
```

#### 推广费排名缓存Key
```
assistant:rank:promotion:{pt}:{timeDim}:{page}:{size}
```

**参数说明:**
- `pt`: 时间分区，如"20241201"
- `timeDim`: 时间维度，YEAR或MONTH
- `page`: 页码
- `size`: 每页大小

### 缓存配置
- **缓存空间**: `assistant:rank:`
- **默认TTL**: 30分钟（如果计算到24点的时间超过30分钟）
- **序列化**: JSON格式

## 使用方法

### 1. 自动缓存
调用排名查询方法时会自动使用缓存：

```java
// 第一次调用会查询数据库并缓存结果
PageInfo<AssistantSalerRankDTO> result1 = assistantSalerService.pageAssessConvertRank(query);

// 第二次调用相同参数会直接从缓存返回
PageInfo<AssistantSalerRankDTO> result2 = assistantSalerService.pageAssessConvertRank(query);
```

### 2. 手动清理缓存

#### 通过Service方法
```java
// 清理指定日期的排名缓存
assistantSalerService.clearRankCache("20241201");

// 清理所有排名缓存
assistantSalerService.clearRankCache(null);
```

#### 通过HTTP接口
```bash
# 清理指定日期的缓存
POST /assistant/saler/rank/cache/clear?pt=20241201

# 清理所有缓存
POST /assistant/saler/rank/cache/clear
```

## 缓存生命周期

### 自动过期
1. **每日24点**: 所有当天的缓存数据自动过期
2. **系统重启**: Redis重启时缓存数据丢失
3. **内存不足**: Redis内存不足时可能会清理部分缓存

### 手动清理时机
1. **数据更新**: 当排名相关数据发生变化时
2. **系统维护**: 定期清理缓存释放内存
3. **异常情况**: 缓存数据异常时强制清理

## 性能优化

### 缓存命中率
- **相同查询**: 相同参数的查询会命中缓存
- **不同分页**: 不同页码会产生不同的缓存Key
- **时间维度**: 年度和月度数据分别缓存

### 内存使用
- **数据大小**: 每个缓存条目包含完整的分页数据
- **过期清理**: 24点自动清理避免内存堆积
- **压缩存储**: JSON序列化相对紧凑

## 监控和调试

### 缓存状态检查
```java
// 检查缓存是否存在
String cacheKey = "assistant:rank:assessConvert:20241201:MONTH:1:10";
boolean exists = redisUtil.exists(cacheKey);
```

### 性能测试
运行测试类中的`testCachePerformance`方法可以对比缓存前后的性能差异。

### 日志输出
- 缓存命中: 无特殊日志
- 缓存未命中: 输出"缓存排名数据成功"
- 缓存异常: 输出错误日志但不影响业务

## 注意事项

1. **数据一致性**: 缓存期间数据库更新不会立即反映到查询结果
2. **内存占用**: 大量缓存数据会占用Redis内存
3. **网络延迟**: Redis网络异常时会降级到数据库查询
4. **并发安全**: 多个请求同时查询相同数据时可能会重复查询数据库

## 故障处理

### 缓存读取失败
- **现象**: 日志显示"读取缓存失败"
- **处理**: 自动降级到数据库查询
- **影响**: 性能下降但功能正常

### 缓存写入失败
- **现象**: 日志显示"缓存排名数据失败"
- **处理**: 继续返回查询结果
- **影响**: 下次查询无法使用缓存

### Redis连接异常
- **现象**: Redis连接超时或断开
- **处理**: 所有缓存操作失败，降级到数据库
- **影响**: 性能下降，需要修复Redis连接
