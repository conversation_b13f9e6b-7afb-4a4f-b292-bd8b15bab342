package com.cfpamf.ms.insur.report.service.assistant.impl;

import com.cfpamf.ms.insur.report.constant.BaseConstants;
import com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceEmpMarketingProgressDfpMapper;
import com.cfpamf.ms.insur.report.dao.safepg.DwaSafesPhoenixTodoEmpMapper;
import com.cfpamf.ms.insur.report.enums.DateTypeEnum;
import com.cfpamf.ms.insur.report.enums.EnumAssistantTimeDim;
import com.cfpamf.ms.insur.report.pojo.dto.AssistantSalerBasicDTO;
import com.cfpamf.ms.insur.report.pojo.dto.AssistantSalerRankDTO;
import com.cfpamf.ms.insur.report.pojo.dto.AssistantSalerSingleTrendDTO;
import com.cfpamf.ms.insur.report.pojo.po.AdsInsuranceEmpMarketingProgressDfp;
import com.cfpamf.ms.insur.report.pojo.po.DwaSafesPhoenixTodoEmp;
import com.cfpamf.ms.insur.report.pojo.query.AssistantSalerRankQuery;
import com.cfpamf.ms.insur.report.service.assistant.AssistantSalerService;
import com.cfpamf.ms.insur.report.util.RedisUtil;
import com.cfpamf.ms.insur.report.constant.CacheKeyConstants;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import org.springframework.util.StringUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.TextStyle;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class AssistantSalerServiceImpl implements AssistantSalerService {
    @Autowired
    private AdsInsuranceEmpMarketingProgressDfpMapper adsInsuranceEmpMarketingProgressDfpMapper;
    @Autowired
    private DwaSafesPhoenixTodoEmpMapper dwaSafesPhoenixTodoEmpMapper;
    @Autowired
    protected RedisUtil<String, String> redisUtil;

    @Override
    public AssistantSalerBasicDTO getAssistantSalerData(String empCode, String pt) {
        AdsInsuranceEmpMarketingProgressDfp dfp = adsInsuranceEmpMarketingProgressDfpMapper.queryById(empCode, pt);
        DwaSafesPhoenixTodoEmp dwaSafesPhoenixTodoEmp = dwaSafesPhoenixTodoEmpMapper.queryByEmpCode(empCode, pt);
        AssistantSalerBasicDTO dto = new AssistantSalerBasicDTO();
        if (dwaSafesPhoenixTodoEmp != null) {
            BeanUtils.copyProperties(dwaSafesPhoenixTodoEmp, dto);
        }
        if (dfp != null) {
            BeanUtils.copyProperties(dfp, dto);
            //异业保费配比统一修改数据单位为“元/元”，数据库原单位“元/万元”
            if(dfp.getSyOfflineLoanInsuranceRate()!=null){
                dto.setSyOfflineLoanInsuranceRate(BigDecimal.valueOf(dfp.getSyOfflineLoanInsuranceRate()).setScale(4,BigDecimal.ROUND_HALF_UP).doubleValue());
            }
            if (dfp.getSmOfflineLoanInsuranceRateTarget()!=null){
                dto.setSmOfflineLoanInsuranceRateTarget(BigDecimal.valueOf(dfp.getSmOfflineLoanInsuranceRateTarget()).setScale(4,BigDecimal.ROUND_HALF_UP).doubleValue());
            }
        }
        return dto;
    }

    /**
     * 获取销售助理的基本信息
     * @param empCode 员工代码
     * @param bchCode 分支编码
     * @param areaCode 区域编码
     * @param pt 日期分区
     * @param dateTypeEnum 月度/年度类型
     * @return 销售助理的基本信息
     */
    @Override
    public AssistantSalerBasicDTO getAssistantSalerRank(String empCode, String bchCode, String areaCode, String pt, DateTypeEnum dateTypeEnum) {
        AssistantSalerBasicDTO assistantSalerBasicDTO = new AssistantSalerBasicDTO();
        assistantSalerBasicDTO.setEmpCode(empCode);
        assistantSalerBasicDTO.setBchCode(bchCode);
        assistantSalerBasicDTO.setAreaCode(areaCode);
        //员工分支排名
        AdsInsuranceEmpMarketingProgressDfp empBchRank = adsInsuranceEmpMarketingProgressDfpMapper.queryRankBch(empCode, bchCode, pt,dateTypeEnum.getCode());
        if (empBchRank != null){
            AdsInsuranceEmpMarketingProgressDfp empBchRankPrevious = adsInsuranceEmpMarketingProgressDfpMapper.queryRankBchPrevious(bchCode, empBchRank.getRankInBch(), pt,dateTypeEnum.getCode());
            if(empBchRankPrevious != null){
                if (dateTypeEnum.equals(DateTypeEnum.month)) {//计算与上一名的差值
                    empBchRank.setGapWithPreviousInBch(empBchRankPrevious.getSmAssessConvertInsuranceAmt() - empBchRank.getSmAssessConvertInsuranceAmt());
                } else {
                    empBchRank.setGapWithPreviousInBch(empBchRankPrevious.getSyAssessConvertInsuranceAmt() - empBchRank.getSyAssessConvertInsuranceAmt());
                }
                assistantSalerBasicDTO.setGapWithPreviousInBch(BigDecimal.valueOf(empBchRank.getGapWithPreviousInBch()).setScale(4, RoundingMode.HALF_UP).doubleValue());
            }
            assistantSalerBasicDTO.setRankInBch(empBchRank.getRankInBch());
        }
        //员工区域排名
        AdsInsuranceEmpMarketingProgressDfp empAreaRank = adsInsuranceEmpMarketingProgressDfpMapper.queryRankArea(empCode, areaCode, pt,dateTypeEnum.getCode());
        if(empAreaRank != null){
            AdsInsuranceEmpMarketingProgressDfp empAreaRankPrevious = adsInsuranceEmpMarketingProgressDfpMapper.queryRankAreaPrevious(areaCode, empAreaRank.getRankInArea(), pt,dateTypeEnum.getCode());
            if (empAreaRankPrevious != null){
                if (dateTypeEnum.equals(DateTypeEnum.month)) {//计算与上一名的差值
                    empAreaRank.setGapWithPreviousInArea(empAreaRankPrevious.getSmAssessConvertInsuranceAmt() - empAreaRank.getSmAssessConvertInsuranceAmt());
                } else {
                    empAreaRank.setGapWithPreviousInArea(empAreaRankPrevious.getSyAssessConvertInsuranceAmt() - empAreaRank.getSyAssessConvertInsuranceAmt());
                }
                assistantSalerBasicDTO.setGapWithPreviousInArea(BigDecimal.valueOf(empAreaRank.getGapWithPreviousInArea()).setScale(4, RoundingMode.HALF_UP).doubleValue());
            }
            assistantSalerBasicDTO.setRankInArea(empAreaRank.getRankInArea());
        }
        //员工全国排名
        AdsInsuranceEmpMarketingProgressDfp empCountryRank = adsInsuranceEmpMarketingProgressDfpMapper.queryRankCountry(empCode, pt,dateTypeEnum.getCode());
        if (empCountryRank != null){
            AdsInsuranceEmpMarketingProgressDfp empCountryRankPrevious = adsInsuranceEmpMarketingProgressDfpMapper.queryRankCountryPrevious(empCountryRank.getRankInCountry(), pt,dateTypeEnum.getCode());
            if (empCountryRankPrevious != null){
                if (dateTypeEnum.equals(DateTypeEnum.month)) {//计算与上一名的差值
                    empCountryRank.setGapWithPreviousInCountry(empCountryRankPrevious.getSmAssessConvertInsuranceAmt() - empCountryRank.getSmAssessConvertInsuranceAmt());
                } else {
                    empCountryRank.setGapWithPreviousInCountry(empCountryRankPrevious.getSyAssessConvertInsuranceAmt() - empCountryRank.getSyAssessConvertInsuranceAmt());
                }
                assistantSalerBasicDTO.setGapWithPreviousInCountry(BigDecimal.valueOf(empCountryRank.getGapWithPreviousInCountry()).setScale(4, RoundingMode.HALF_UP).doubleValue());
            }
            assistantSalerBasicDTO.setRankInCountry(empCountryRank.getRankInCountry());
        }

        return assistantSalerBasicDTO;

    }

    @Override
    public AssistantSalerSingleTrendDTO queryEmpAmtTrend(String empCode, List<String> ptListCurrentYear) {
        List<AdsInsuranceEmpMarketingProgressDfp> list = adsInsuranceEmpMarketingProgressDfpMapper.queryEmpAmtTrend(empCode, ptListCurrentYear);
        if (list != null && list.size() > 0) {
            AssistantSalerSingleTrendDTO assistantSalerSingleTrendDTO = new AssistantSalerSingleTrendDTO();
            assistantSalerSingleTrendDTO.setTrendName("当前标准保费趋势");
            assistantSalerSingleTrendDTO.setPtList(ptListCurrentYear.stream().map(this::convertPtToMonth).collect(Collectors.toList()));
            assistantSalerSingleTrendDTO.setValueList(list.stream()
                    .map(item -> {
                        return BigDecimal.valueOf(Optional.ofNullable(item.getSmAssessConvertInsuranceAmt()).orElse(0.0)).setScale(4, RoundingMode.HALF_UP).doubleValue();
                    }).collect(Collectors.toList()));
            assistantSalerSingleTrendDTO.setStandradList(list.stream()
                    .map(item -> {
                                return BigDecimal.valueOf(Optional.ofNullable(item.getSmAssessConvertInsuranceAmtTarget()).orElse(0.0)).setScale(4, RoundingMode.HALF_UP).doubleValue();
                            }).collect(Collectors.toList()));
            return assistantSalerSingleTrendDTO;
        }
        return null;
    }

    private String convertPtToMonth(String s) {
        LocalDate date = LocalDate.parse(s, BaseConstants.FMT_YYYYMMDD);
        String monthString = date.getMonth().getDisplayName(TextStyle.SHORT, Locale.getDefault());
        return monthString;
    }

    @Override
    public AssistantSalerSingleTrendDTO queryEmpATypeCustTransRateTrend(String empCode, List<String> ptListCurrentYear) {
        List<AdsInsuranceEmpMarketingProgressDfp> list = adsInsuranceEmpMarketingProgressDfpMapper.queryEmpATypeCustTransRateTrend(empCode, ptListCurrentYear);
        if (list != null && list.size() > 0) {
            AssistantSalerSingleTrendDTO assistantSalerSingleTrendDTO = new AssistantSalerSingleTrendDTO();
            assistantSalerSingleTrendDTO.setTrendName("当前A类客户转化趋势");
            assistantSalerSingleTrendDTO.setPtList(ptListCurrentYear.stream().map(this::convertPtToMonth).collect(Collectors.toList()));
            assistantSalerSingleTrendDTO.setValueList(list.stream()
                    .map(item -> {
                                return BigDecimal.valueOf(Optional.ofNullable(item.getSyLoanCustTransRate()).orElse(0.0)).setScale(4, RoundingMode.HALF_UP).doubleValue();
                            }).collect(Collectors.toList()));
            assistantSalerSingleTrendDTO.setStandradList(list.stream()
                    .map(item -> {
                                return BigDecimal.valueOf(Optional.ofNullable(item.getSmLoanCustTransRateTarget()).orElse(0.0)).setScale(4, RoundingMode.HALF_UP).doubleValue();
                            }).collect(Collectors.toList()));
            return assistantSalerSingleTrendDTO;
        }
        return null;
    }

    @Override
    public AssistantSalerSingleTrendDTO queryEmpATypeCustInsuranceRateTrend(String empCode, List<String> ptListCurrentYear) {
        List<AdsInsuranceEmpMarketingProgressDfp> list = adsInsuranceEmpMarketingProgressDfpMapper.queryEmpATypeCustInsuranceRateTrend(empCode, ptListCurrentYear);
        if (list != null && list.size() > 0) {
            AssistantSalerSingleTrendDTO assistantSalerSingleTrendDTO = new AssistantSalerSingleTrendDTO();
            assistantSalerSingleTrendDTO.setTrendName("客户合作深度趋势");
            assistantSalerSingleTrendDTO.setPtList(ptListCurrentYear.stream().map(this::convertPtToMonth).collect(Collectors.toList()));
            //异业保费配比统一修改数据单位为“元/元”，数据库原单位“元/万元”
            assistantSalerSingleTrendDTO.setValueList(list.stream()
                    .map(item->{
                        return BigDecimal.valueOf(Optional.ofNullable(item.getSyOfflineLoanInsuranceRate()).orElse(0.0)).setScale(4, RoundingMode.HALF_UP).doubleValue();
                    }).collect(Collectors.toList()));
            assistantSalerSingleTrendDTO.setStandradList(list.stream()
                    .map(item->{
                        return BigDecimal.valueOf(Optional.ofNullable(item.getSmOfflineLoanInsuranceRateTarget()).orElse(0.0)).setScale(4, RoundingMode.HALF_UP).doubleValue();
                    }).collect(Collectors.toList()));
            return assistantSalerSingleTrendDTO;
        }
        return null;
    }

    @Override
    public AssistantSalerSingleTrendDTO queryEmpRetentionRate(String empCode, List<String> ptListCurrentYear) {
        List<AdsInsuranceEmpMarketingProgressDfp> list = adsInsuranceEmpMarketingProgressDfpMapper.queryEmpRetentionRate(empCode, ptListCurrentYear);
        if (list != null && list.size() > 0) {
            AssistantSalerSingleTrendDTO assistantSalerSingleTrendDTO = new AssistantSalerSingleTrendDTO();
            assistantSalerSingleTrendDTO.setTrendName("当前客户留存趋势");
            assistantSalerSingleTrendDTO.setPtList(ptListCurrentYear.stream().map(this::convertPtToMonth).collect(Collectors.toList()));
            assistantSalerSingleTrendDTO.setValueList(list.stream()
                    .map(item->{
                                return BigDecimal.valueOf(Optional.ofNullable(item.getSyInsuranceRetentionRate()).orElse(0.0)).setScale(4, RoundingMode.HALF_UP).doubleValue();
                            }).collect(Collectors.toList()));
            assistantSalerSingleTrendDTO.setStandradList(list.stream()
                    .map(item->{
                                return BigDecimal.valueOf(Optional.ofNullable(item.getSmInsuranceRetentionRateTarget()).orElse(0.0)).setScale(4, RoundingMode.HALF_UP).doubleValue();
                            }).collect(Collectors.toList()));
            return assistantSalerSingleTrendDTO;
        }
        return null;
    }

    /**
     * 获取销售助理的基本信息
     * @param empCode 员工代码
     * @param bchCode 分支编码
     * @param areaCode 区域编码
     * @param pt 日期分区
     * @param dateTypeEnum 月度/年度类型
     * @return 销售助理的基本信息
     */
    @Override
    public AssistantSalerBasicDTO getAssistantSalerARank(String empCode, String bchCode, String areaCode, String pt, DateTypeEnum dateTypeEnum) {
        AssistantSalerBasicDTO assistantSalerBasicDTO = new AssistantSalerBasicDTO();
        assistantSalerBasicDTO.setEmpCode(empCode);
        assistantSalerBasicDTO.setBchCode(bchCode);
        assistantSalerBasicDTO.setAreaCode(areaCode);
        //员工分支排名
        AdsInsuranceEmpMarketingProgressDfp empBchRank = adsInsuranceEmpMarketingProgressDfpMapper.queryRankBch(empCode, bchCode, pt,dateTypeEnum.getCode());
        if (empBchRank != null){
            AdsInsuranceEmpMarketingProgressDfp empBchRankPrevious = adsInsuranceEmpMarketingProgressDfpMapper.queryRankBchPrevious(bchCode, empBchRank.getRankInBch(), pt,dateTypeEnum.getCode());
            if(empBchRankPrevious != null){
                if (dateTypeEnum.equals(DateTypeEnum.month)) {//计算与上一名的差值
                    empBchRank.setGapWithPreviousInBch(empBchRankPrevious.getSmAssessConvertInsuranceAmt() - empBchRank.getSmAssessConvertInsuranceAmt());
                } else {
                    empBchRank.setGapWithPreviousInBch(empBchRankPrevious.getSyAssessConvertInsuranceAmt() - empBchRank.getSyAssessConvertInsuranceAmt());
                }
                assistantSalerBasicDTO.setGapWithPreviousInBch(BigDecimal.valueOf(empBchRank.getGapWithPreviousInBch()).setScale(4, RoundingMode.HALF_UP).doubleValue());
            }
            assistantSalerBasicDTO.setRankInBch(empBchRank.getRankInBch());
        }
        //员工区域排名
        AdsInsuranceEmpMarketingProgressDfp empAreaRank = adsInsuranceEmpMarketingProgressDfpMapper.queryRankArea(empCode, areaCode, pt,dateTypeEnum.getCode());
        if(empAreaRank != null){
            AdsInsuranceEmpMarketingProgressDfp empAreaRankPrevious = adsInsuranceEmpMarketingProgressDfpMapper.queryRankAreaPrevious(areaCode, empAreaRank.getRankInArea(), pt,dateTypeEnum.getCode());
            if (empAreaRankPrevious != null){
                if (dateTypeEnum.equals(DateTypeEnum.month)) {//计算与上一名的差值
                    empAreaRank.setGapWithPreviousInArea(empAreaRankPrevious.getSmAssessConvertInsuranceAmt() - empAreaRank.getSmAssessConvertInsuranceAmt());
                } else {
                    empAreaRank.setGapWithPreviousInArea(empAreaRankPrevious.getSyAssessConvertInsuranceAmt() - empAreaRank.getSyAssessConvertInsuranceAmt());
                }
                assistantSalerBasicDTO.setGapWithPreviousInArea(BigDecimal.valueOf(empAreaRank.getGapWithPreviousInArea()).setScale(4, RoundingMode.HALF_UP).doubleValue());
            }
            assistantSalerBasicDTO.setRankInArea(empAreaRank.getRankInArea());
        }
        //员工全国排名
        AdsInsuranceEmpMarketingProgressDfp empCountryRank = adsInsuranceEmpMarketingProgressDfpMapper.queryRankCountry(empCode, pt,dateTypeEnum.getCode());
        if (empCountryRank != null){
            AdsInsuranceEmpMarketingProgressDfp empCountryRankPrevious = adsInsuranceEmpMarketingProgressDfpMapper.queryRankCountryPrevious(empCountryRank.getRankInCountry(), pt,dateTypeEnum.getCode());
            if (empCountryRankPrevious != null){
                if (dateTypeEnum.equals(DateTypeEnum.month)) {//计算与上一名的差值
                    empCountryRank.setGapWithPreviousInCountry(empCountryRankPrevious.getSmAssessConvertInsuranceAmt() - empCountryRank.getSmAssessConvertInsuranceAmt());
                } else {
                    empCountryRank.setGapWithPreviousInCountry(empCountryRankPrevious.getSyAssessConvertInsuranceAmt() - empCountryRank.getSyAssessConvertInsuranceAmt());
                }
                assistantSalerBasicDTO.setGapWithPreviousInCountry(BigDecimal.valueOf(empCountryRank.getGapWithPreviousInCountry()).setScale(4, RoundingMode.HALF_UP).doubleValue());
            }
            assistantSalerBasicDTO.setRankInCountry(empCountryRank.getRankInCountry());
        }

        return assistantSalerBasicDTO;

    }

    @Override
    public PageInfo<AssistantSalerRankDTO> pageAssessConvertRank(AssistantSalerRankQuery query){
        // 生成缓存key
        String cacheKey = buildAssessConvertRankCacheKey(query);

        // 尝试从缓存获取数据
        PageInfo<AssistantSalerRankDTO> cachedResult = getCachedRankData(cacheKey);
        if (cachedResult != null) {
            return cachedResult;
        }

        // 缓存未命中，查询数据库
        PageInfo<AdsInsuranceEmpMarketingProgressDfp> pageInfo = PageHelper.startPage(query.getPage(), query.getSize(), query.isCount())
                .doSelectPageInfo(() -> queryAssessConvertRankList(query));

        // 转换数据并拷贝分页信息
        PageInfo<AssistantSalerRankDTO> result = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, result);

        // 转换数据列表
        List<AssistantSalerRankDTO> rankDTOList = pageInfo.getList().stream()
                .map(item -> convertToRankDTO(item, query.getTimeDim()))
                .collect(Collectors.toList());

        result.setList(rankDTOList);

        // 缓存结果到当天24点
        cacheRankDataUntilMidnight(cacheKey, result);

        return result;
    }

    @Override
    public PageInfo<AssistantSalerRankDTO> pagePromotionRank(AssistantSalerRankQuery query){
        // 生成缓存key
        String cacheKey = buildPromotionRankCacheKey(query);

        // 尝试从缓存获取数据
        PageInfo<AssistantSalerRankDTO> cachedResult = getCachedRankData(cacheKey);
        if (cachedResult != null) {
            return cachedResult;
        }

        // 缓存未命中，查询数据库
        PageInfo<AdsInsuranceEmpMarketingProgressDfp> pageInfo = PageHelper.startPage(query.getPage(), query.getSize(), query.isCount())
                .doSelectPageInfo(() -> queryPromotionRankList(query));

        // 转换数据并拷贝分页信息
        PageInfo<AssistantSalerRankDTO> result = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, result);

        // 转换数据列表
        List<AssistantSalerRankDTO> rankDTOList = pageInfo.getList().stream()
                .map(item -> convertToPromotionRankDTO(item, query.getTimeDim()))
                .collect(Collectors.toList());

        result.setList(rankDTOList);

        // 缓存结果到当天24点
        cacheRankDataUntilMidnight(cacheKey, result);

        return result;
    }








    /**
     * 查询标准保费排名列表
     * @param query 查询参数
     * @return 排名列表
     */
    private List<AdsInsuranceEmpMarketingProgressDfp> queryAssessConvertRankList(AssistantSalerRankQuery query) {
        // 根据时间维度确定数据类型
        String dataType = query.getTimeDim() == EnumAssistantTimeDim.YEAR ? DateTypeEnum.year.getCode() : DateTypeEnum.month.getCode();
        return adsInsuranceEmpMarketingProgressDfpMapper.listAssessConvertRankCountry(query.getPt(), dataType);
    }

    /**
     * 查询推广费排名列表
     * @param query 查询参数
     * @return 排名列表
     */
    private List<AdsInsuranceEmpMarketingProgressDfp> queryPromotionRankList(AssistantSalerRankQuery query) {
        // 根据时间维度确定数据类型
        String dataType = query.getTimeDim() == EnumAssistantTimeDim.YEAR ? DateTypeEnum.year.getCode() : DateTypeEnum.month.getCode();
        return adsInsuranceEmpMarketingProgressDfpMapper.listPromotionRankCountry(query.getPt(), dataType);
    }

    /**
     * 转换标准保费排名数据为DTO
     * @param entity 实体对象
     * @param timeDim 时间维度
     * @return DTO对象
     */
    private AssistantSalerRankDTO convertToRankDTO(AdsInsuranceEmpMarketingProgressDfp entity, EnumAssistantTimeDim timeDim) {
        AssistantSalerRankDTO dto = new AssistantSalerRankDTO();
        dto.setEmpName(entity.getEmpName());
        dto.setEmpCode(entity.getEmpCode());
        dto.setBchName(entity.getBchName());
        dto.setBchCode(entity.getBchCode());
        dto.setDistrictName(entity.getDistrictName());
        dto.setDistrictCode(entity.getDistrictCode());
        dto.setAreaName(entity.getAreaName());
        dto.setAreaCode(entity.getAreaCode());
        dto.setRank(entity.getRankInCountry());

        // 根据时间维度设置金额
        if (timeDim == EnumAssistantTimeDim.YEAR) {
            dto.setAmt(formatAmount(entity.getSyAssessConvertInsuranceAmt()));
        } else {
            dto.setAmt(formatAmount(entity.getSmAssessConvertInsuranceAmt()));
        }

        return dto;
    }

    /**
     * 转换推广费排名数据为DTO
     * @param entity 实体对象
     * @param timeDim 时间维度
     * @return DTO对象
     */
    private AssistantSalerRankDTO convertToPromotionRankDTO(AdsInsuranceEmpMarketingProgressDfp entity, EnumAssistantTimeDim timeDim) {
        AssistantSalerRankDTO dto = new AssistantSalerRankDTO();
        dto.setEmpName(entity.getEmpName());
        dto.setEmpCode(entity.getEmpCode());
        dto.setBchName(entity.getBchName());
        dto.setBchCode(entity.getBchCode());
        dto.setDistrictName(entity.getDistrictName());
        dto.setDistrictCode(entity.getDistrictCode());
        dto.setAreaName(entity.getAreaName());
        dto.setAreaCode(entity.getAreaCode());
        dto.setRank(entity.getRankInCountry());

        // 根据时间维度设置推广费金额
        if (timeDim == EnumAssistantTimeDim.YEAR) {
            dto.setAmt(formatAmount(entity.getSyGrantAmount()));
        } else {
            dto.setAmt(formatAmount(entity.getSmGrantAmount()));
        }

        return dto;
    }

    /**
     * 格式化金额，保留4位小数
     * @param amount 原始金额
     * @return 格式化后的金额
     */
    private Double formatAmount(Double amount) {
        if (amount == null) {
            return 0.00;
        }
        return BigDecimal.valueOf(amount).setScale(4, RoundingMode.HALF_UP).doubleValue();
    }

    /**
     * 构建标准保费排名缓存key
     * @param query 查询参数
     * @return 缓存key
     */
    private String buildAssessConvertRankCacheKey(AssistantSalerRankQuery query) {
        return CacheKeyConstants.ASSESS_CONVERT_RANK +
               query.getPt() + ":" +
               query.getTimeDim().name() + ":" +
               query.getPage() + ":" +
               query.getSize();
    }

    /**
     * 构建推广费排名缓存key
     * @param query 查询参数
     * @return 缓存key
     */
    private String buildPromotionRankCacheKey(AssistantSalerRankQuery query) {
        return CacheKeyConstants.PROMOTION_RANK +
               query.getPt() + ":" +
               query.getTimeDim().name() + ":" +
               query.getPage() + ":" +
               query.getSize();
    }

    /**
     * 从缓存获取排名数据
     * @param cacheKey 缓存key
     * @return 排名数据，如果缓存不存在返回null
     */
    private PageInfo<AssistantSalerRankDTO> getCachedRankData(String cacheKey) {
        try {
            String cachedJson = redisUtil.get(cacheKey);
            if (!StringUtils.isEmpty(cachedJson)) {
                return JSON.parseObject(cachedJson, new TypeReference<PageInfo<AssistantSalerRankDTO>>() {});
            }
        } catch (Exception e) {
            // 缓存读取失败，记录日志但不影响业务流程
            System.err.println("读取缓存失败: " + cacheKey + ", 错误: " + e.getMessage());
        }
        return null;
    }

    /**
     * 缓存排名数据到当天24点
     * @param cacheKey 缓存key
     * @param data 要缓存的数据
     */
    private void cacheRankDataUntilMidnight(String cacheKey, PageInfo<AssistantSalerRankDTO> data) {
        try {
            // 计算到当天24点的剩余秒数
            long secondsUntilMidnight = getSecondsUntilMidnight();

            // 将数据序列化为JSON并缓存
            String jsonData = JSON.toJSONString(data);
            redisUtil.set(cacheKey, jsonData, secondsUntilMidnight);

            System.out.println("缓存排名数据成功: " + cacheKey + ", 过期时间: " + secondsUntilMidnight + "秒");
        } catch (Exception e) {
            // 缓存写入失败，记录日志但不影响业务流程
            System.err.println("缓存排名数据失败: " + cacheKey + ", 错误: " + e.getMessage());
        }
    }

    /**
     * 计算到当天24点的剩余秒数
     * @return 剩余秒数
     */
    private long getSecondsUntilMidnight() {
        LocalDate today = LocalDate.now();
        LocalDate tomorrow = today.plusDays(1);

        // 当天24点的时间戳
        long midnightTimestamp = tomorrow.atStartOfDay().atZone(java.time.ZoneId.systemDefault()).toEpochSecond();
        // 当前时间戳
        long currentTimestamp = System.currentTimeMillis() / 1000;

        // 计算剩余秒数，至少保留60秒避免立即过期
        long secondsUntilMidnight = midnightTimestamp - currentTimestamp;
        return Math.max(secondsUntilMidnight, 60L);
    }

    /**
     * 清理排名缓存
     * @param pt 时间分区，如果为null则清理所有排名缓存
     */
    public void clearRankCache(String pt) {
        try {
            if (StringUtils.isEmpty(pt)) {
                // 清理所有排名缓存
                redisUtil.removePattern(CacheKeyConstants.ASSESS_CONVERT_RANK + "*");
                redisUtil.removePattern(CacheKeyConstants.PROMOTION_RANK + "*");
                System.out.println("已清理所有排名缓存");
            } else {
                // 清理指定日期的排名缓存
                redisUtil.removePattern(CacheKeyConstants.ASSESS_CONVERT_RANK + pt + "*");
                redisUtil.removePattern(CacheKeyConstants.PROMOTION_RANK + pt + "*");
                System.out.println("已清理日期 " + pt + " 的排名缓存");
            }
        } catch (Exception e) {
            System.err.println("清理排名缓存失败: " + e.getMessage());
        }
    }

    private String dateToPT(Date date) {
        return null;
    }
    private Date ptToDate(String pt) {
        return null;
    }
}
