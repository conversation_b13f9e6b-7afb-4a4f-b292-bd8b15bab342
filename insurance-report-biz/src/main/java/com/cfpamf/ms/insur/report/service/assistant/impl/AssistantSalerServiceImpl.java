package com.cfpamf.ms.insur.report.service.assistant.impl;

import com.cfpamf.ms.insur.report.constant.BaseConstants;
import com.cfpamf.ms.insur.report.dao.safepg.AdsInsuranceEmpMarketingProgressDfpMapper;
import com.cfpamf.ms.insur.report.dao.safepg.DwaSafesPhoenixTodoEmpMapper;
import com.cfpamf.ms.insur.report.enums.DateTypeEnum;
import com.cfpamf.ms.insur.report.pojo.dto.AssistantSalerBasicDTO;
import com.cfpamf.ms.insur.report.pojo.dto.AssistantSalerRankDTO;
import com.cfpamf.ms.insur.report.pojo.dto.AssistantSalerSingleTrendDTO;
import com.cfpamf.ms.insur.report.pojo.po.AdsInsuranceEmpMarketingProgressDfp;
import com.cfpamf.ms.insur.report.pojo.po.DwaSafesPhoenixTodoEmp;
import com.cfpamf.ms.insur.report.pojo.query.AssistantSalerRankQuery;
import com.cfpamf.ms.insur.report.service.assistant.AssistantSalerService;
import com.github.pagehelper.PageException;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.TextStyle;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class AssistantSalerServiceImpl implements AssistantSalerService {
    @Autowired
    private AdsInsuranceEmpMarketingProgressDfpMapper adsInsuranceEmpMarketingProgressDfpMapper;
    @Autowired
    private DwaSafesPhoenixTodoEmpMapper dwaSafesPhoenixTodoEmpMapper;


    @Override
    public AssistantSalerBasicDTO getAssistantSalerData(String empCode, String pt) {
        AdsInsuranceEmpMarketingProgressDfp dfp = adsInsuranceEmpMarketingProgressDfpMapper.queryById(empCode, pt);
        DwaSafesPhoenixTodoEmp dwaSafesPhoenixTodoEmp = dwaSafesPhoenixTodoEmpMapper.queryByEmpCode(empCode, pt);
        AssistantSalerBasicDTO dto = new AssistantSalerBasicDTO();
        if (dwaSafesPhoenixTodoEmp != null) {
            BeanUtils.copyProperties(dwaSafesPhoenixTodoEmp, dto);
        }
        if (dfp != null) {
            BeanUtils.copyProperties(dfp, dto);
            //异业保费配比统一修改数据单位为“元/元”，数据库原单位“元/万元”
            if(dfp.getSyOfflineLoanInsuranceRate()!=null){
                dto.setSyOfflineLoanInsuranceRate(BigDecimal.valueOf(dfp.getSyOfflineLoanInsuranceRate()).setScale(4,BigDecimal.ROUND_HALF_UP).doubleValue());
            }
            if (dfp.getSmOfflineLoanInsuranceRateTarget()!=null){
                dto.setSmOfflineLoanInsuranceRateTarget(BigDecimal.valueOf(dfp.getSmOfflineLoanInsuranceRateTarget()).setScale(4,BigDecimal.ROUND_HALF_UP).doubleValue());
            }
        }
        return dto;
    }

    /**
     * 获取销售助理的基本信息
     * @param empCode 员工代码
     * @param bchCode 分支编码
     * @param areaCode 区域编码
     * @param pt 日期分区
     * @param dateTypeEnum 月度/年度类型
     * @return 销售助理的基本信息
     */
    @Override
    public AssistantSalerBasicDTO getAssistantSalerRank(String empCode, String bchCode, String areaCode, String pt, DateTypeEnum dateTypeEnum) {
        AssistantSalerBasicDTO assistantSalerBasicDTO = new AssistantSalerBasicDTO();
        assistantSalerBasicDTO.setEmpCode(empCode);
        assistantSalerBasicDTO.setBchCode(bchCode);
        assistantSalerBasicDTO.setAreaCode(areaCode);
        //员工分支排名
        AdsInsuranceEmpMarketingProgressDfp empBchRank = adsInsuranceEmpMarketingProgressDfpMapper.queryRankBch(empCode, bchCode, pt,dateTypeEnum.getCode());
        if (empBchRank != null){
            AdsInsuranceEmpMarketingProgressDfp empBchRankPrevious = adsInsuranceEmpMarketingProgressDfpMapper.queryRankBchPrevious(bchCode, empBchRank.getRankInBch(), pt,dateTypeEnum.getCode());
            if(empBchRankPrevious != null){
                if (dateTypeEnum.equals(DateTypeEnum.month)) {//计算与上一名的差值
                    empBchRank.setGapWithPreviousInBch(empBchRankPrevious.getSmAssessConvertInsuranceAmt() - empBchRank.getSmAssessConvertInsuranceAmt());
                } else {
                    empBchRank.setGapWithPreviousInBch(empBchRankPrevious.getSyAssessConvertInsuranceAmt() - empBchRank.getSyAssessConvertInsuranceAmt());
                }
                assistantSalerBasicDTO.setGapWithPreviousInBch(BigDecimal.valueOf(empBchRank.getGapWithPreviousInBch()).setScale(4, RoundingMode.HALF_UP).doubleValue());
            }
            assistantSalerBasicDTO.setRankInBch(empBchRank.getRankInBch());
        }
        //员工区域排名
        AdsInsuranceEmpMarketingProgressDfp empAreaRank = adsInsuranceEmpMarketingProgressDfpMapper.queryRankArea(empCode, areaCode, pt,dateTypeEnum.getCode());
        if(empAreaRank != null){
            AdsInsuranceEmpMarketingProgressDfp empAreaRankPrevious = adsInsuranceEmpMarketingProgressDfpMapper.queryRankAreaPrevious(areaCode, empAreaRank.getRankInArea(), pt,dateTypeEnum.getCode());
            if (empAreaRankPrevious != null){
                if (dateTypeEnum.equals(DateTypeEnum.month)) {//计算与上一名的差值
                    empAreaRank.setGapWithPreviousInArea(empAreaRankPrevious.getSmAssessConvertInsuranceAmt() - empAreaRank.getSmAssessConvertInsuranceAmt());
                } else {
                    empAreaRank.setGapWithPreviousInArea(empAreaRankPrevious.getSyAssessConvertInsuranceAmt() - empAreaRank.getSyAssessConvertInsuranceAmt());
                }
                assistantSalerBasicDTO.setGapWithPreviousInArea(BigDecimal.valueOf(empAreaRank.getGapWithPreviousInArea()).setScale(4, RoundingMode.HALF_UP).doubleValue());
            }
            assistantSalerBasicDTO.setRankInArea(empAreaRank.getRankInArea());
        }
        //员工全国排名
        AdsInsuranceEmpMarketingProgressDfp empCountryRank = adsInsuranceEmpMarketingProgressDfpMapper.queryRankCountry(empCode, pt,dateTypeEnum.getCode());
        if (empCountryRank != null){
            AdsInsuranceEmpMarketingProgressDfp empCountryRankPrevious = adsInsuranceEmpMarketingProgressDfpMapper.queryRankCountryPrevious(empCountryRank.getRankInCountry(), pt,dateTypeEnum.getCode());
            if (empCountryRankPrevious != null){
                if (dateTypeEnum.equals(DateTypeEnum.month)) {//计算与上一名的差值
                    empCountryRank.setGapWithPreviousInCountry(empCountryRankPrevious.getSmAssessConvertInsuranceAmt() - empCountryRank.getSmAssessConvertInsuranceAmt());
                } else {
                    empCountryRank.setGapWithPreviousInCountry(empCountryRankPrevious.getSyAssessConvertInsuranceAmt() - empCountryRank.getSyAssessConvertInsuranceAmt());
                }
                assistantSalerBasicDTO.setGapWithPreviousInCountry(BigDecimal.valueOf(empCountryRank.getGapWithPreviousInCountry()).setScale(4, RoundingMode.HALF_UP).doubleValue());
            }
            assistantSalerBasicDTO.setRankInCountry(empCountryRank.getRankInCountry());
        }

        return assistantSalerBasicDTO;

    }

    @Override
    public AssistantSalerSingleTrendDTO queryEmpAmtTrend(String empCode, List<String> ptListCurrentYear) {
        List<AdsInsuranceEmpMarketingProgressDfp> list = adsInsuranceEmpMarketingProgressDfpMapper.queryEmpAmtTrend(empCode, ptListCurrentYear);
        if (list != null && list.size() > 0) {
            AssistantSalerSingleTrendDTO assistantSalerSingleTrendDTO = new AssistantSalerSingleTrendDTO();
            assistantSalerSingleTrendDTO.setTrendName("当前标准保费趋势");
            assistantSalerSingleTrendDTO.setPtList(ptListCurrentYear.stream().map(this::convertPtToMonth).collect(Collectors.toList()));
            assistantSalerSingleTrendDTO.setValueList(list.stream()
                    .map(item -> {
                        return BigDecimal.valueOf(Optional.ofNullable(item.getSmAssessConvertInsuranceAmt()).orElse(0.0)).setScale(4, RoundingMode.HALF_UP).doubleValue();
                    }).collect(Collectors.toList()));
            assistantSalerSingleTrendDTO.setStandradList(list.stream()
                    .map(item -> {
                                return BigDecimal.valueOf(Optional.ofNullable(item.getSmAssessConvertInsuranceAmtTarget()).orElse(0.0)).setScale(4, RoundingMode.HALF_UP).doubleValue();
                            }).collect(Collectors.toList()));
            return assistantSalerSingleTrendDTO;
        }
        return null;
    }

    private String convertPtToMonth(String s) {
        LocalDate date = LocalDate.parse(s, BaseConstants.FMT_YYYYMMDD);
        String monthString = date.getMonth().getDisplayName(TextStyle.SHORT, Locale.getDefault());
        return monthString;
    }

    @Override
    public AssistantSalerSingleTrendDTO queryEmpATypeCustTransRateTrend(String empCode, List<String> ptListCurrentYear) {
        List<AdsInsuranceEmpMarketingProgressDfp> list = adsInsuranceEmpMarketingProgressDfpMapper.queryEmpATypeCustTransRateTrend(empCode, ptListCurrentYear);
        if (list != null && list.size() > 0) {
            AssistantSalerSingleTrendDTO assistantSalerSingleTrendDTO = new AssistantSalerSingleTrendDTO();
            assistantSalerSingleTrendDTO.setTrendName("当前A类客户转化趋势");
            assistantSalerSingleTrendDTO.setPtList(ptListCurrentYear.stream().map(this::convertPtToMonth).collect(Collectors.toList()));
            assistantSalerSingleTrendDTO.setValueList(list.stream()
                    .map(item -> {
                                return BigDecimal.valueOf(Optional.ofNullable(item.getSyLoanCustTransRate()).orElse(0.0)).setScale(4, RoundingMode.HALF_UP).doubleValue();
                            }).collect(Collectors.toList()));
            assistantSalerSingleTrendDTO.setStandradList(list.stream()
                    .map(item -> {
                                return BigDecimal.valueOf(Optional.ofNullable(item.getSmLoanCustTransRateTarget()).orElse(0.0)).setScale(4, RoundingMode.HALF_UP).doubleValue();
                            }).collect(Collectors.toList()));
            return assistantSalerSingleTrendDTO;
        }
        return null;
    }

    @Override
    public AssistantSalerSingleTrendDTO queryEmpATypeCustInsuranceRateTrend(String empCode, List<String> ptListCurrentYear) {
        List<AdsInsuranceEmpMarketingProgressDfp> list = adsInsuranceEmpMarketingProgressDfpMapper.queryEmpATypeCustInsuranceRateTrend(empCode, ptListCurrentYear);
        if (list != null && list.size() > 0) {
            AssistantSalerSingleTrendDTO assistantSalerSingleTrendDTO = new AssistantSalerSingleTrendDTO();
            assistantSalerSingleTrendDTO.setTrendName("客户合作深度趋势");
            assistantSalerSingleTrendDTO.setPtList(ptListCurrentYear.stream().map(this::convertPtToMonth).collect(Collectors.toList()));
            //异业保费配比统一修改数据单位为“元/元”，数据库原单位“元/万元”
            assistantSalerSingleTrendDTO.setValueList(list.stream()
                    .map(item->{
                        return BigDecimal.valueOf(Optional.ofNullable(item.getSyOfflineLoanInsuranceRate()).orElse(0.0)).setScale(4, RoundingMode.HALF_UP).doubleValue();
                    }).collect(Collectors.toList()));
            assistantSalerSingleTrendDTO.setStandradList(list.stream()
                    .map(item->{
                        return BigDecimal.valueOf(Optional.ofNullable(item.getSmOfflineLoanInsuranceRateTarget()).orElse(0.0)).setScale(4, RoundingMode.HALF_UP).doubleValue();
                    }).collect(Collectors.toList()));
            return assistantSalerSingleTrendDTO;
        }
        return null;
    }

    @Override
    public AssistantSalerSingleTrendDTO queryEmpRetentionRate(String empCode, List<String> ptListCurrentYear) {
        List<AdsInsuranceEmpMarketingProgressDfp> list = adsInsuranceEmpMarketingProgressDfpMapper.queryEmpRetentionRate(empCode, ptListCurrentYear);
        if (list != null && list.size() > 0) {
            AssistantSalerSingleTrendDTO assistantSalerSingleTrendDTO = new AssistantSalerSingleTrendDTO();
            assistantSalerSingleTrendDTO.setTrendName("当前客户留存趋势");
            assistantSalerSingleTrendDTO.setPtList(ptListCurrentYear.stream().map(this::convertPtToMonth).collect(Collectors.toList()));
            assistantSalerSingleTrendDTO.setValueList(list.stream()
                    .map(item->{
                                return BigDecimal.valueOf(Optional.ofNullable(item.getSyInsuranceRetentionRate()).orElse(0.0)).setScale(4, RoundingMode.HALF_UP).doubleValue();
                            }).collect(Collectors.toList()));
            assistantSalerSingleTrendDTO.setStandradList(list.stream()
                    .map(item->{
                                return BigDecimal.valueOf(Optional.ofNullable(item.getSmInsuranceRetentionRateTarget()).orElse(0.0)).setScale(4, RoundingMode.HALF_UP).doubleValue();
                            }).collect(Collectors.toList()));
            return assistantSalerSingleTrendDTO;
        }
        return null;
    }

    /**
     * 获取销售助理的基本信息
     * @param empCode 员工代码
     * @param bchCode 分支编码
     * @param areaCode 区域编码
     * @param pt 日期分区
     * @param dateTypeEnum 月度/年度类型
     * @return 销售助理的基本信息
     */
    @Override
    public AssistantSalerBasicDTO getAssistantSalerARank(String empCode, String bchCode, String areaCode, String pt, DateTypeEnum dateTypeEnum) {
        AssistantSalerBasicDTO assistantSalerBasicDTO = new AssistantSalerBasicDTO();
        assistantSalerBasicDTO.setEmpCode(empCode);
        assistantSalerBasicDTO.setBchCode(bchCode);
        assistantSalerBasicDTO.setAreaCode(areaCode);
        //员工分支排名
        AdsInsuranceEmpMarketingProgressDfp empBchRank = adsInsuranceEmpMarketingProgressDfpMapper.queryRankBch(empCode, bchCode, pt,dateTypeEnum.getCode());
        if (empBchRank != null){
            AdsInsuranceEmpMarketingProgressDfp empBchRankPrevious = adsInsuranceEmpMarketingProgressDfpMapper.queryRankBchPrevious(bchCode, empBchRank.getRankInBch(), pt,dateTypeEnum.getCode());
            if(empBchRankPrevious != null){
                if (dateTypeEnum.equals(DateTypeEnum.month)) {//计算与上一名的差值
                    empBchRank.setGapWithPreviousInBch(empBchRankPrevious.getSmAssessConvertInsuranceAmt() - empBchRank.getSmAssessConvertInsuranceAmt());
                } else {
                    empBchRank.setGapWithPreviousInBch(empBchRankPrevious.getSyAssessConvertInsuranceAmt() - empBchRank.getSyAssessConvertInsuranceAmt());
                }
                assistantSalerBasicDTO.setGapWithPreviousInBch(BigDecimal.valueOf(empBchRank.getGapWithPreviousInBch()).setScale(4, RoundingMode.HALF_UP).doubleValue());
            }
            assistantSalerBasicDTO.setRankInBch(empBchRank.getRankInBch());
        }
        //员工区域排名
        AdsInsuranceEmpMarketingProgressDfp empAreaRank = adsInsuranceEmpMarketingProgressDfpMapper.queryRankArea(empCode, areaCode, pt,dateTypeEnum.getCode());
        if(empAreaRank != null){
            AdsInsuranceEmpMarketingProgressDfp empAreaRankPrevious = adsInsuranceEmpMarketingProgressDfpMapper.queryRankAreaPrevious(areaCode, empAreaRank.getRankInArea(), pt,dateTypeEnum.getCode());
            if (empAreaRankPrevious != null){
                if (dateTypeEnum.equals(DateTypeEnum.month)) {//计算与上一名的差值
                    empAreaRank.setGapWithPreviousInArea(empAreaRankPrevious.getSmAssessConvertInsuranceAmt() - empAreaRank.getSmAssessConvertInsuranceAmt());
                } else {
                    empAreaRank.setGapWithPreviousInArea(empAreaRankPrevious.getSyAssessConvertInsuranceAmt() - empAreaRank.getSyAssessConvertInsuranceAmt());
                }
                assistantSalerBasicDTO.setGapWithPreviousInArea(BigDecimal.valueOf(empAreaRank.getGapWithPreviousInArea()).setScale(4, RoundingMode.HALF_UP).doubleValue());
            }
            assistantSalerBasicDTO.setRankInArea(empAreaRank.getRankInArea());
        }
        //员工全国排名
        AdsInsuranceEmpMarketingProgressDfp empCountryRank = adsInsuranceEmpMarketingProgressDfpMapper.queryRankCountry(empCode, pt,dateTypeEnum.getCode());
        if (empCountryRank != null){
            AdsInsuranceEmpMarketingProgressDfp empCountryRankPrevious = adsInsuranceEmpMarketingProgressDfpMapper.queryRankCountryPrevious(empCountryRank.getRankInCountry(), pt,dateTypeEnum.getCode());
            if (empCountryRankPrevious != null){
                if (dateTypeEnum.equals(DateTypeEnum.month)) {//计算与上一名的差值
                    empCountryRank.setGapWithPreviousInCountry(empCountryRankPrevious.getSmAssessConvertInsuranceAmt() - empCountryRank.getSmAssessConvertInsuranceAmt());
                } else {
                    empCountryRank.setGapWithPreviousInCountry(empCountryRankPrevious.getSyAssessConvertInsuranceAmt() - empCountryRank.getSyAssessConvertInsuranceAmt());
                }
                assistantSalerBasicDTO.setGapWithPreviousInCountry(BigDecimal.valueOf(empCountryRank.getGapWithPreviousInCountry()).setScale(4, RoundingMode.HALF_UP).doubleValue());
            }
            assistantSalerBasicDTO.setRankInCountry(empCountryRank.getRankInCountry());
        }

        return assistantSalerBasicDTO;

    }

    public PageInfo<AssistantSalerRankDTO> pageAssessConvertRank(AssistantSalerRankQuery query){
        // 使用PageHelper进行分页查询
        PageInfo<AdsInsuranceEmpMarketingProgressDfp> pageInfo = PageHelper.startPage(query.getPage(), query.getSize(), query.isCount())
                .doSelectPageInfo(() -> queryAssessConvertRankList(query));

        // 转换数据并拷贝分页信息
        PageInfo<AssistantSalerRankDTO> result = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, result);

        // 转换数据列表
        List<AssistantSalerRankDTO> rankDTOList = pageInfo.getList().stream()
                .map(this::convertToRankDTO)
                .collect(Collectors.toList());

        result.setList(rankDTOList);
        return result;
    }

    public PageInfo<AssistantSalerRankDTO> pagePromotionRank(AssistantSalerRankQuery query){
        return null;
    }








    private String dateToPT(Date date) {
        return null;
    }
    private Date ptToDate(String pt) {
        return null;
    }
}
