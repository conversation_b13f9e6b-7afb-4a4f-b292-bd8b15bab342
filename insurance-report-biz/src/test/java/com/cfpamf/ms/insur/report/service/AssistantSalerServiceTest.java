package com.cfpamf.ms.insur.report.service;

import com.cfpamf.ms.insur.report.enums.EnumAssistantTimeDim;
import com.cfpamf.ms.insur.report.pojo.dto.AssistantSalerRankDTO;
import com.cfpamf.ms.insur.report.pojo.query.AssistantSalerRankQuery;
import com.cfpamf.ms.insur.report.service.assistant.AssistantSalerService;
import com.github.pagehelper.PageInfo;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * AssistantSalerService测试类
 */
@SpringBootTest
public class AssistantSalerServiceTest {

    @Autowired
    private AssistantSalerService assistantSalerService;

    @Test
    public void testPageAssessConvertRank() {
        // 创建查询参数
        AssistantSalerRankQuery query = new AssistantSalerRankQuery();
        query.setPt("20241201"); // 设置测试日期
        query.setTimeDim(EnumAssistantTimeDim.MONTH); // 月度数据
        query.setPage(1);
        query.setSize(10);
        query.setCount(true);

        try {
            // 调用分页查询方法
            PageInfo<AssistantSalerRankDTO> result = assistantSalerService.pageAssessConvertRank(query);
            
            // 验证结果
            System.out.println("标准保费排名查询结果:");
            System.out.println("总记录数: " + result.getTotal());
            System.out.println("当前页数据量: " + result.getList().size());
            
            // 打印前几条数据
            result.getList().stream().limit(5).forEach(item -> {
                System.out.println(String.format("排名: %d, 员工: %s, 分支: %s, 金额: %.4f", 
                    item.getRank(), item.getEmpName(), item.getBchName(), item.getAmt()));
            });
            
        } catch (Exception e) {
            System.err.println("标准保费排名查询测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testPagePromotionRank() {
        // 创建查询参数
        AssistantSalerRankQuery query = new AssistantSalerRankQuery();
        query.setPt("20241201"); // 设置测试日期
        query.setTimeDim(EnumAssistantTimeDim.YEAR); // 年度数据
        query.setPage(1);
        query.setSize(10);
        query.setCount(true);

        try {
            // 调用分页查询方法
            PageInfo<AssistantSalerRankDTO> result = assistantSalerService.pagePromotionRank(query);
            
            // 验证结果
            System.out.println("推广费排名查询结果:");
            System.out.println("总记录数: " + result.getTotal());
            System.out.println("当前页数据量: " + result.getList().size());
            
            // 打印前几条数据
            result.getList().stream().limit(5).forEach(item -> {
                System.out.println(String.format("排名: %d, 员工: %s, 分支: %s, 推广费: %.4f", 
                    item.getRank(), item.getEmpName(), item.getBchName(), item.getAmt()));
            });
            
        } catch (Exception e) {
            System.err.println("推广费排名查询测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
