package com.cfpamf.ms.insur.report.service;

import com.cfpamf.ms.insur.report.enums.EnumAssistantTimeDim;
import com.cfpamf.ms.insur.report.pojo.dto.AssistantSalerRankDTO;
import com.cfpamf.ms.insur.report.pojo.query.AssistantSalerRankQuery;
import com.cfpamf.ms.insur.report.service.assistant.AssistantSalerService;
import com.github.pagehelper.PageInfo;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * AssistantSalerService测试类
 */
@SpringBootTest
public class AssistantSalerServiceTest {

    @Autowired
    private AssistantSalerService assistantSalerService;

    @Test
    public void testPageAssessConvertRank() {
        // 创建查询参数
        AssistantSalerRankQuery query = new AssistantSalerRankQuery();
        query.setPt("20241201"); // 设置测试日期
        query.setTimeDim(EnumAssistantTimeDim.MONTH); // 月度数据
        query.setPage(1);
        query.setSize(10);
        query.setCount(true);

        try {
            // 调用分页查询方法
            PageInfo<AssistantSalerRankDTO> result = assistantSalerService.pageAssessConvertRank(query);
            
            // 验证结果
            System.out.println("标准保费排名查询结果:");
            System.out.println("总记录数: " + result.getTotal());
            System.out.println("当前页数据量: " + result.getList().size());
            
            // 打印前几条数据
            result.getList().stream().limit(5).forEach(item -> {
                System.out.println(String.format("排名: %d, 员工: %s, 分支: %s, 金额: %.4f", 
                    item.getRank(), item.getEmpName(), item.getBchName(), item.getAmt()));
            });
            
        } catch (Exception e) {
            System.err.println("标准保费排名查询测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testPagePromotionRank() {
        // 创建查询参数
        AssistantSalerRankQuery query = new AssistantSalerRankQuery();
        query.setPt("20241201"); // 设置测试日期
        query.setTimeDim(EnumAssistantTimeDim.YEAR); // 年度数据
        query.setPage(1);
        query.setSize(10);
        query.setCount(true);

        try {
            // 调用分页查询方法
            PageInfo<AssistantSalerRankDTO> result = assistantSalerService.pagePromotionRank(query);
            
            // 验证结果
            System.out.println("推广费排名查询结果:");
            System.out.println("总记录数: " + result.getTotal());
            System.out.println("当前页数据量: " + result.getList().size());
            
            // 打印前几条数据
            result.getList().stream().limit(5).forEach(item -> {
                System.out.println(String.format("排名: %d, 员工: %s, 分支: %s, 推广费: %.4f", 
                    item.getRank(), item.getEmpName(), item.getBchName(), item.getAmt()));
            });
            
        } catch (Exception e) {
            System.err.println("推广费排名查询测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testCachePerformance() {
        // 创建查询参数
        AssistantSalerRankQuery query = new AssistantSalerRankQuery();
        query.setPt("20241201");
        query.setTimeDim(EnumAssistantTimeDim.MONTH);
        query.setPage(1);
        query.setSize(10);
        query.setCount(true);

        try {
            // 第一次查询（从数据库）
            long startTime1 = System.currentTimeMillis();
            PageInfo<AssistantSalerRankDTO> result1 = assistantSalerService.pageAssessConvertRank(query);
            long endTime1 = System.currentTimeMillis();

            // 第二次查询（从缓存）
            long startTime2 = System.currentTimeMillis();
            PageInfo<AssistantSalerRankDTO> result2 = assistantSalerService.pageAssessConvertRank(query);
            long endTime2 = System.currentTimeMillis();

            System.out.println("缓存性能测试:");
            System.out.println("第一次查询耗时: " + (endTime1 - startTime1) + "ms (数据库)");
            System.out.println("第二次查询耗时: " + (endTime2 - startTime2) + "ms (缓存)");
            System.out.println("数据一致性: " + (result1.getTotal() == result2.getTotal()));

        } catch (Exception e) {
            System.err.println("缓存性能测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testClearCache() {
        try {
            // 清理指定日期的缓存
            assistantSalerService.clearRankCache("20241201");
            System.out.println("清理指定日期缓存测试通过");

            // 清理所有缓存
            assistantSalerService.clearRankCache(null);
            System.out.println("清理所有缓存测试通过");

        } catch (Exception e) {
            System.err.println("清理缓存测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
